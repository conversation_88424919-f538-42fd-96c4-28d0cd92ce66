import React from "react";
import {motion} from "framer-motion";
import {
  Instagram,
  Twitter,
  Facebook,
  Youtube,
  ChevronDown,
  Menu,
} from "lucide-react";

const Hero = () => {
  const socialLinks = [
    {icon: Instagram, href: "#", label: "Instagram"},
    {icon: Twitter, href: "#", label: "Twitter"},
    {icon: Facebook, href: "#", label: "Facebook"},
    {icon: Youtube, href: "#", label: "YouTube"},
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black overflow-hidden p-6">
      {/* Floating 3D geometric shapes outside border */}
      <motion.div
        className="absolute top-16 right-16 w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-600 transform rotate-12 rounded-lg shadow-2xl"
        animate={{
          rotate: [12, 25, 12],
          y: [0, -10, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        style={{
          boxShadow:
            "0 10px 30px rgba(249, 115, 22, 0.4), 0 0 20px rgba(249, 115, 22, 0.3)",
        }}
      />
      <motion.div
        className="absolute bottom-20 left-20 w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-700 transform rotate-45 rounded-lg shadow-2xl"
        animate={{
          rotate: [45, 60, 45],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        style={{
          boxShadow:
            "0 8px 25px rgba(249, 115, 22, 0.4), 0 0 15px rgba(249, 115, 22, 0.3)",
        }}
      />
      <motion.div
        className="absolute bottom-32 right-32 w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 transform rotate-12 rounded-lg shadow-2xl"
        animate={{
          rotate: [12, -12, 12],
          x: [0, 5, 0],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        style={{
          boxShadow:
            "0 6px 20px rgba(249, 115, 22, 0.4), 0 0 12px rgba(249, 115, 22, 0.3)",
        }}
      />
      {/* Main Glowing Border Container */}
      <div
        className="relative h-full min-h-[calc(100vh-3rem)] rounded-3xl bg-gradient-to-br from-gray-800/40 via-gray-900/60 to-black/80 backdrop-blur-sm"
        style={{
          border: "3px solid transparent",
          backgroundImage:
            "linear-gradient(135deg, rgba(31, 41, 55, 0.8), rgba(17, 24, 39, 0.9), rgba(0, 0, 0, 0.95)), linear-gradient(135deg, #f97316, #ea580c)",
          backgroundOrigin: "border-box",
          backgroundClip: "content-box, border-box",
          boxShadow:
            "0 0 60px rgba(249, 115, 22, 0.3), inset 0 0 60px rgba(249, 115, 22, 0.1)",
        }}
      >
        {/* Top Navigation */}
        <nav className="absolute top-0 left-0 right-0 z-50 pt-8 px-8">
          <div className="flex justify-between items-center">
            {/* Menu Icon */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="text-white hover:text-orange-500 transition-colors cursor-pointer"
            >
              <Menu size={24} />
            </motion.div>

            {/* Brand Name */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl font-bold"
            >
              <span className="text-white">Black</span>
              <span className="text-orange-500">Orange</span>
            </motion.div>

            {/* Empty space for balance */}
            <div className="w-6"></div>
          </div>
        </nav>

        {/* Main Content Layout */}
        <div className="flex items-center justify-between h-full px-8 pt-20 pb-8">
          {/* Left Side - Social Icons and Content */}
          <div className="flex items-center space-x-16">
            {/* Vertical Social Icons */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col space-y-6"
            >
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <motion.a
                    key={social.label}
                    href={social.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                    whileHover={{ scale: 1.2, color: '#f97316' }}
                    className="text-gray-400 hover:text-orange-500 transition-all duration-300"
                  >
                    <Icon size={20} />
                  </motion.a>
                );
              })}
            </motion.div>

            {/* Main Content */}
            <div className="max-w-lg">
              {/* Hello Text */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="flex items-center space-x-4 mb-6"
              >
                <div className="w-16 h-px bg-gray-600"></div>
                <span className="text-gray-400 text-lg font-light">Hello</span>
              </motion.div>

              {/* Main Headline */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                className="text-5xl md:text-6xl font-bold mb-6 leading-tight"
              >
                <span className="text-white">I'm </span>
                <span className="text-white">Yadav Rana</span>
              </motion.h1>

              {/* Subheading */}
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="text-gray-400 text-lg mb-8 leading-relaxed"
              >
                A full stack developer based in Toronto.
              </motion.p>

              {/* CTA Button */}
              <motion.button
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.4 }}
                whileHover={{ scale: 1.05, boxShadow: '0 10px 30px rgba(249, 115, 22, 0.4)' }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-lg font-semibold shadow-lg hover:shadow-orange-500/25 transition-all duration-300"
              >
                Learn more
              </motion.button>
            </div>
          </div>

        {/* Navigation */}
        <nav className="absolute top-0 left-0 right-0 z-50 pt-8 px-8 pb-8">
          <div className="flex justify-between items-center max-w-7xl mx-auto">
            {/* Empty div for spacing */}
            <div className="w-10"></div>

            {/* Centered Name */}
            <motion.div
              initial={{opacity: 0, y: -20}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.6}}
              className="absolute left-1/2 transform -translate-x-1/2"
            >
              <span className="text-2xl font-bold">
                <span className="text-white">Yadav</span>
                <span className="text-orange-500">Rana</span>
              </span>
            </motion.div>

            {/* Hamburger Menu */}
            <motion.button
              initial={{opacity: 0, x: 20}}
              animate={{opacity: 1, x: 0}}
              transition={{duration: 0.6, delay: 0.2}}
              className="p-2 text-white hover:text-orange-500 transition-colors"
            >
              <div className="space-y-1">
                <div className="w-6 h-0.5 bg-current"></div>
                <div className="w-6 h-0.5 bg-current"></div>
                <div className="w-6 h-0.5 bg-current"></div>
              </div>
            </motion.button>
          </div>
        </nav>

        {/* Main Content */}
        <div className="flex items-center justify-between h-full px-16 py-20">
          {/* Left Side - Social Links */}
          <motion.div
            initial={{opacity: 0, x: -50}}
            animate={{opacity: 1, x: 0}}
            transition={{duration: 0.8, delay: 0.4}}
            className="flex flex-col space-y-6 z-40"
          >
            {socialLinks.map((social, index) => (
              <motion.a
                key={social.label}
                href={social.href}
                initial={{opacity: 0, y: 20}}
                animate={{opacity: 1, y: 0}}
                transition={{duration: 0.5, delay: 0.6 + index * 0.1}}
                whileHover={{scale: 1.2, color: "#f97316"}}
                className="text-gray-400 hover:text-orange-500 transition-colors"
              >
                <social.icon size={24} />
              </motion.a>
            ))}
          </motion.div>

          {/* Center Content */}
          <div className="flex-1 max-w-2xl">
            {/* Hello Text with Line */}
            <motion.div
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.2}}
              className="flex items-center space-x-4 mb-6"
            >
              <div className="w-16 h-px bg-gray-600"></div>
              <span className="text-gray-400 text-lg font-light">Hello</span>
            </motion.div>

            <motion.h1
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.4}}
              className="text-5xl lg:text-6xl font-bold mb-6 leading-tight"
            >
              <span className="text-white">I'm Yadav Rana</span>
            </motion.h1>

            <motion.p
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.6}}
              className="text-gray-400 text-lg mb-8 leading-relaxed"
            >
              A full stack developer based in Toronto.
            </motion.p>

            <motion.button
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.8}}
              whileHover={{scale: 1.05, boxShadow: "0 10px 30px rgba(249, 115, 22, 0.4)"}}
              whileTap={{scale: 0.95}}
              className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-8 py-4 rounded-lg font-semibold shadow-lg hover:shadow-orange-500/25 transition-all duration-300"
            >
              Learn more
            </motion.button>
          </div>

          {/* Right Side - 3D Character */}
          <motion.div
            initial={{opacity: 0, scale: 0.8}}
            animate={{opacity: 1, scale: 1}}
            transition={{duration: 1, delay: 0.6}}
            className="hidden lg:block flex-1 relative"
          >
            <div className="relative w-full h-96 flex items-center justify-center">
              {/* 3D Character */}
              <motion.div
                animate={{
                  y: [0, -8, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="relative"
              >
                {/* Character Container */}
                <div className="relative w-64 h-64">
                  {/* Head */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full shadow-2xl">
                    {/* Orange Headphones */}
                    <div className="absolute -inset-2 border-4 border-orange-500 rounded-full shadow-lg"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      {/* Eyes */}
                      <div className="flex space-x-3">
                        <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                        <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
                      </div>
                    </div>
                    {/* Smile */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-4 h-2 border-b-2 border-gray-700 rounded-full"></div>
                  </div>

                  {/* Body */}
                  <div className="absolute top-16 left-1/2 transform -translate-x-1/2 w-24 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-3xl shadow-2xl">
                    {/* Arms */}
                    <div className="absolute -left-8 top-6 w-16 h-6 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full transform -rotate-20 shadow-lg"></div>
                    <div className="absolute -right-8 top-6 w-16 h-6 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full transform rotate-20 shadow-lg"></div>
                  </div>

                  {/* Laptop */}
                  <div className="absolute top-28 left-1/2 transform -translate-x-1/2 w-20 h-12 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg shadow-2xl z-10">
                    {/* Screen */}
                    <div className="w-full h-8 bg-gradient-to-br from-gray-800 to-gray-900 rounded-t-lg relative">
                      <div className="absolute inset-2 bg-gradient-to-br from-blue-900 to-purple-900 rounded opacity-80"></div>
                      <div className="absolute top-2 left-2 w-2 h-2 bg-green-400 rounded-full opacity-60"></div>
                    </div>
                    {/* Keyboard */}
                    <div className="w-full h-4 bg-gradient-to-br from-gray-300 to-gray-400 rounded-b-lg"></div>
                  </div>

                  {/* Legs (cross-legged) */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
                    <div className="relative">
                      <div className="absolute -left-6 w-12 h-16 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full transform rotate-45 shadow-lg"></div>
                      <div className="absolute -right-6 w-12 h-16 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full transform -rotate-45 shadow-lg"></div>
                      {/* Feet */}
                      <div className="absolute -left-8 bottom-2 w-8 h-4 bg-gradient-to-r from-gray-800 to-gray-900 rounded-full shadow-md"></div>
                      <div className="absolute -right-8 bottom-2 w-8 h-4 bg-gradient-to-r from-gray-800 to-gray-900 rounded-full shadow-md"></div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{opacity: 0}}
          animate={{opacity: 1}}
          transition={{duration: 0.8, delay: 1.2}}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{y: [0, 10, 0]}}
            transition={{duration: 2, repeat: Infinity}}
            className="flex flex-col items-center text-gray-400"
          >
            <span className="text-sm mb-2 transform -rotate-90 origin-center">
              Scroll down
            </span>
            <ChevronDown size={20} />
          </motion.div>
        </motion.div>
      </div>{" "}
      {/* Close glowing border container */}
    </div>
  );
};

export default Hero;
