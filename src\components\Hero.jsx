import {motion} from "framer-motion";
import {Instagram, Twitter, Facebook, Youtube, ChevronDown} from "lucide-react";

const Hero = () => {
  const socialLinks = [
    {icon: Instagram, href: "#", label: "Instagram"},
    {icon: Twitter, href: "#", label: "Twitter"},
    {icon: Facebook, href: "#", label: "Facebook"},
    {icon: Youtube, href: "#", label: "YouTube"},
  ];

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black overflow-hidden p-8">
      {/* Glowing Border Container */}
      <div
        className="relative h-full min-h-[calc(100vh-4rem)] rounded-3xl border-2 border-orange-500/30 bg-gradient-to-br from-gray-800/50 via-gray-900/50 to-black/50 backdrop-blur-sm shadow-2xl shadow-orange-500/20"
        style={{
          boxShadow:
            "0 0 50px rgba(249, 115, 22, 0.3), inset 0 0 50px rgba(249, 115, 22, 0.1)",
        }}
      >
        {/* Floating geometric shapes */}
        <motion.div
          className="absolute top-20 right-20 w-16 h-16 bg-orange-500 transform rotate-45"
          animate={{
            rotate: [45, 90, 45],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        <motion.div
          className="absolute bottom-32 left-16 w-12 h-12 bg-orange-400 transform rotate-45"
          animate={{
            rotate: [0, 45, 0],
            y: [0, -10, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        <motion.div
          className="absolute top-1/3 left-1/4 w-8 h-8 bg-orange-300 transform rotate-45"
          animate={{
            rotate: [45, 135, 45],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />

        {/* Navigation */}
        <nav className="absolute top-0 left-0 right-0 z-50 pt-20 px-8 pb-8">
          <div className="flex justify-between items-center max-w-7xl mx-auto">
            {/* Empty div for spacing */}
            <div className="w-10"></div>

            {/* Centered Name */}
            <motion.div
              initial={{opacity: 0, y: -20}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.6}}
              className="absolute left-1/2 transform -translate-x-1/2"
            >
              <span className="text-2xl font-bold">
                <span className="text-white">Yadav</span>
                <span className="text-orange-500">Rana</span>
              </span>
            </motion.div>

            {/* Hamburger Menu */}
            <motion.button
              initial={{opacity: 0, x: 20}}
              animate={{opacity: 1, x: 0}}
              transition={{duration: 0.6, delay: 0.2}}
              className="p-2 text-white hover:text-orange-500 transition-colors"
            >
              <div className="space-y-1">
                <div className="w-6 h-0.5 bg-current"></div>
                <div className="w-6 h-0.5 bg-current"></div>
                <div className="w-6 h-0.5 bg-current"></div>
              </div>
            </motion.button>
          </div>
        </nav>

        {/* Main Content */}
        <div className="flex items-center justify-center min-h-screen px-20 py-20">
          {/* Left Side - Social Links */}
          <motion.div
            initial={{opacity: 0, x: -50}}
            animate={{opacity: 1, x: 0}}
            transition={{duration: 0.8, delay: 0.4}}
            className="fixed left-20 top-1/2 transform -translate-y-1/2 z-40"
          >
            <div className="flex flex-col space-y-6">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  initial={{opacity: 0, y: 20}}
                  animate={{opacity: 1, y: 0}}
                  transition={{duration: 0.5, delay: 0.6 + index * 0.1}}
                  whileHover={{scale: 1.2, color: "#f97316"}}
                  className="text-gray-400 hover:text-orange-500 transition-colors"
                >
                  <social.icon size={24} />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Center Content */}
          <div className="flex-1 text-center max-w-4xl mx-auto">
            <motion.div
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.2}}
              className="mb-4"
            >
              <span className="text-orange-500 text-lg font-medium">Hello</span>
            </motion.div>

            <motion.h1
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.4}}
              className="text-5xl lg:text-7xl font-bold mb-6 leading-tight"
            >
              I'm <span className="text-white">Yadav Rana</span>
            </motion.h1>

            <motion.p
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.6}}
              className="text-gray-300 text-lg mb-8 max-w-md"
            >
              A full stack developer based in Toronto.
            </motion.p>

            <motion.button
              initial={{opacity: 0, y: 30}}
              animate={{opacity: 1, y: 0}}
              transition={{duration: 0.8, delay: 0.8}}
              whileHover={{scale: 1.05}}
              whileTap={{scale: 0.95}}
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Learn more
            </motion.button>
          </div>

          {/* Right Side - Character */}
          <motion.div
            initial={{opacity: 0, scale: 0.8}}
            animate={{opacity: 1, scale: 1}}
            transition={{duration: 1, delay: 0.6}}
            className="hidden lg:block flex-1 relative"
          >
            <div className="relative w-full h-96 flex items-center justify-center">
              {/* Placeholder for your 3D character */}
              <div className="w-80 h-80 bg-gradient-to-br from-gray-700 to-gray-800 rounded-full flex items-center justify-center">
                <span className="text-gray-400 text-lg">
                  <img src="./assets/Character.jpg" alt="" />
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{opacity: 0}}
          animate={{opacity: 1}}
          transition={{duration: 0.8, delay: 1.2}}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{y: [0, 10, 0]}}
            transition={{duration: 2, repeat: Infinity}}
            className="flex flex-col items-center text-gray-400"
          >
            <span className="text-sm mb-2 transform -rotate-90 origin-center">
              Scroll down
            </span>
            <ChevronDown size={20} />
          </motion.div>
        </motion.div>
      </div>{" "}
      {/* Close glowing border container */}
    </div>
  );
};

export default Hero;
